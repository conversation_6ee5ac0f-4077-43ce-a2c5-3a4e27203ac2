# 产品类型管理功能说明

## 功能概述
产品类型管理页面提供了对产品类型的查看、编辑和状态管理功能。根据用户需求，该页面只包含编辑和状态修改操作，不包含新增和删除功能。

## 功能特性
- ✅ 产品类型列表查询（支持按业务类型、类型名称、状态筛选）
- ✅ 产品类型编辑
- ✅ 产品类型状态修改（启用/停用）
- ✅ 数据导出功能
- ❌ 新增产品类型（按需求不提供）
- ❌ 删除产品类型（按需求不提供）

## 部署步骤

### 1. 数据库更新
执行以下SQL文件来添加必要的菜单和字段：

```sql
-- 1. 添加status字段（如果不存在）
source sql/product_type_status_column.sql

-- 2. 添加菜单权限
source sql/product_type_menu.sql
```

### 2. 后端代码
已创建和修改的文件：
- `ruoyi-yanbao/src/main/java/com/ruoyi/yanbao/entity/ProductType.java` - 添加status字段和Excel注解
- `ruoyi-admin/src/main/java/com/ruoyi/web/controller/yanbao/ProductTypeController.java` - 新建控制器
- `ruoyi-yanbao/src/main/java/com/ruoyi/yanbao/service/ProductTypeService.java` - 扩展服务接口
- `ruoyi-yanbao/src/main/java/com/ruoyi/yanbao/service/impl/ProductTypeServiceImpl.java` - 实现服务方法

### 3. 前端代码
已创建的文件：
- `ruoyi-ui/src/api/yanbao/productType.js` - API接口
- `ruoyi-ui/src/views/yanbao/productType/index.vue` - 管理页面

## 页面功能说明

### 查询功能
- 支持按业务类型（数字）筛选
- 支持按类型名称模糊查询
- 支持按状态筛选（有效/无效）

### 编辑功能
- 点击操作列的"修改"按钮可编辑产品类型信息
- 可修改：业务类型、类型名称、状态、前端配置、后端配置

### 状态管理
- 表格中的状态列提供开关按钮，可直接切换启用/停用状态
- 状态变更会有确认提示

### 导出功能
- 支持导出当前查询条件下的产品类型数据
- 导出格式为Excel文件

## 权限配置
系统会自动创建以下权限：
- `yanbao:productType:list` - 产品类型查询
- `yanbao:productType:query` - 产品类型详情查询
- `yanbao:productType:edit` - 产品类型编辑
- `yanbao:productType:export` - 产品类型导出

## 菜单位置
延保管理 > 产品类型管理

## 注意事项
1. 该功能按照用户需求设计，只提供编辑和状态修改功能
2. 如需新增或删除功能，需要额外开发
3. 确保数据库中p_product_type表已存在status字段
4. 确保用户具有相应的菜单和功能权限
