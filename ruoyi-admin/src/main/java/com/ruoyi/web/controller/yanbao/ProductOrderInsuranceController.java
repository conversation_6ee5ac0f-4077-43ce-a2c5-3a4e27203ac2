package com.ruoyi.web.controller.yanbao;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.yanbao.entity.vo.ProductOrderInsuranceImportVo;
import com.ruoyi.yanbao.entity.vo.ProductOrderInsuranceVo;
import com.ruoyi.yanbao.entity.vo.ProductOrderVo;
import com.ruoyi.yanbao.service.ProductOrderInsuranceService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping("/yanbao/insurance")
public class ProductOrderInsuranceController extends BaseController {

    @Autowired
    private ProductOrderInsuranceService productOrderInsuranceService;

    /**
     * 查询投保列表
     */
    @PreAuthorize("@ss.hasPermi('yanbao:insurance:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProductOrderInsuranceVo param) {
        startPage();
        List<ProductOrderInsuranceVo> list = productOrderInsuranceService.queryList(param);
        return getDataTable(list);
    }

    /**
     * 获取投保详情
     */
    @PreAuthorize("@ss.hasPermi('yanbao:insurance:list')")
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        ProductOrderInsuranceVo detail = productOrderInsuranceService.getInsuranceDetail(id);
        return AjaxResult.success(detail);
    }

    /**
     * 投保操作
     */
    @PreAuthorize("@ss.hasPermi('yanbao:insurance:add')")
    @Log(title = "投保操作", businessType = BusinessType.INSERT)
    @PostMapping("/doInsurance")
    public AjaxResult doInsurance(@RequestBody ProductOrderInsuranceVo param) {
        return toAjax(productOrderInsuranceService.doInsurance(param));
    }

    /**
     * 退保操作
     */
    @PreAuthorize("@ss.hasPermi('yanbao:insurance:cancel')")
    @Log(title = "退保操作", businessType = BusinessType.UPDATE)
    @PostMapping("/cancelInsurance/{id}")
    public AjaxResult cancelInsurance(@PathVariable("id") Long id) {
        return toAjax(productOrderInsuranceService.doCancelInsurance(id));
    }

    /**
     * 下载投保导入模板
     */
    @PostMapping("/importTemplate")
    @PreAuthorize("@ss.hasPermi('yanbao:insurance:add')")
    public void importTemplate(HttpServletResponse response, ProductOrderInsuranceVo param) {
        param.setInsuranceState(ProductOrderInsuranceVo.InsuranceState.WAIT);
        param.setOrderState(ProductOrderVo.OrderState.PASS);
        List<ProductOrderInsuranceVo> list = productOrderInsuranceService.queryList(param);
        ExcelUtil<ProductOrderInsuranceImportVo> util = new ExcelUtil<ProductOrderInsuranceImportVo>(ProductOrderInsuranceImportVo.class);
        util.exportExcel(response, convertToImportVoList(list), "投保导入模板");
    }

    /**
     * 批量导入投保数据
     */
    @PreAuthorize("@ss.hasPermi('yanbao:insurance:add')")
    @Log(title = "投保管理", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<ProductOrderInsuranceImportVo> util = new ExcelUtil<ProductOrderInsuranceImportVo>(ProductOrderInsuranceImportVo.class);
        List<ProductOrderInsuranceImportVo> importList = util.importExcel(file.getInputStream());
        List<ProductOrderInsuranceVo> insuranceList = convertFromImportVoList(importList);
        String operName = getUsername();
        String message = productOrderInsuranceService.importInsurance(insuranceList, operName);
        return success(message);
    }

    /**
     * 转换为导入VO列表
     */
    private List<ProductOrderInsuranceImportVo> convertToImportVoList(List<ProductOrderInsuranceVo> list) {
        return list.stream().map(this::convertToImportVo).collect(java.util.stream.Collectors.toList());
    }

    /**
     * 转换为导入VO
     */
    private ProductOrderInsuranceImportVo convertToImportVo(ProductOrderInsuranceVo vo) {
        ProductOrderInsuranceImportVo importVo = new ProductOrderInsuranceImportVo();
        importVo.setOrderId(vo.getProductOrderId());
        importVo.setCustomerName(vo.getCustomerName());
        importVo.setVinNo(vo.getVinNo());
        importVo.setCarNo(vo.getCarNo());
        importVo.setProductName(vo.getProductName());
        importVo.setStoreName(vo.getStoreName());
        importVo.setSaleUserName(vo.getSaleUserName());
        importVo.setInsuranceCompanyName(vo.getInsuranceCompanyName());
        importVo.setInsuranceNo(vo.getInsuranceNo());
        importVo.setInsuranceDate(vo.getInsuranceDate());
        importVo.setBeginTime(vo.getBeginTime());
        importVo.setEndTime(vo.getEndTime());
        importVo.setPrice(vo.getPrice());
        return importVo;
    }

    /**
     * 从导入VO列表转换
     */
    private List<ProductOrderInsuranceVo> convertFromImportVoList(List<ProductOrderInsuranceImportVo> importList) {
        return importList.stream().map(this::convertFromImportVo).collect(java.util.stream.Collectors.toList());
    }

    /**
     * 从导入VO转换
     */
    private ProductOrderInsuranceVo convertFromImportVo(ProductOrderInsuranceImportVo importVo) {
        ProductOrderInsuranceVo vo = new ProductOrderInsuranceVo();
        vo.setProductOrderId(importVo.getOrderId());
        vo.setCustomerName(importVo.getCustomerName());
        vo.setVinNo(importVo.getVinNo());
        vo.setCarNo(importVo.getCarNo());
        vo.setProductName(importVo.getProductName());
        vo.setStoreName(importVo.getStoreName());
        vo.setSaleUserName(importVo.getSaleUserName());
        vo.setInsuranceCompanyName(importVo.getInsuranceCompanyName());
        vo.setInsuranceNo(importVo.getInsuranceNo());
        vo.setInsuranceDate(importVo.getInsuranceDate());
        vo.setBeginTime(importVo.getBeginTime());
        vo.setEndTime(importVo.getEndTime());
        vo.setPrice(importVo.getPrice());
        return vo;
    }
}
