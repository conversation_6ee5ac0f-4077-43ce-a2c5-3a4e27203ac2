package com.ruoyi.web.controller.yanbao;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.yanbao.entity.Product;
import com.ruoyi.yanbao.entity.ProductTerm;
import com.ruoyi.yanbao.entity.ProductType;
import com.ruoyi.yanbao.entity.vo.ProductVo;
import com.ruoyi.yanbao.service.InsuranceCompanyService;
import com.ruoyi.yanbao.service.ProductService;
import com.ruoyi.yanbao.service.ProductTypeService;
import com.ruoyi.yanbao.service.StoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 产品管理Controller
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
@RestController
@RequestMapping("/yanbao/productManage")
public class ProductManageController extends BaseController
{
    @Autowired
    private ProductService productService;

    @Autowired
    private ProductTypeService productTypeService;

    @Autowired
    private InsuranceCompanyService insuranceCompanyService;

    @Autowired
    private StoreService storeService;

    /**
     * 查询产品管理列表
     */
    @PreAuthorize("@ss.hasPermi('yanbao:productManage:list')")
    @GetMapping("/list")
    public TableDataInfo list(Product product)
    {
        startPage();
        List<Product> list = productService.selectProductList(product);
        return getDataTable(list);
    }

    /**
     * 获取产品管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('yanbao:productManage:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        ProductVo productVo = productService.getProductInfo(id);
        return success(productVo);
    }

    /**
     * 新增产品管理
     */
    @PreAuthorize("@ss.hasPermi('yanbao:productManage:add')")
    @Log(title = "产品管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody Map<String, Object> params)
    {
        Product product = new Product();
        // 从params中提取产品信息
        product.setType((Integer) params.get("type"));
        product.setName((String) params.get("name"));
        product.setRemark((String) params.get("remark"));
        product.setSalesMode((Integer) params.get("salesMode"));
        product.setMaximumMileageAvailable(Long.valueOf(params.get("maximumMileageAvailable").toString()));
        product.setMaximumPeriodAvailable(Long.valueOf(params.get("maximumPeriodAvailable").toString()));
        product.setMaximumPeriodAvailableType((Integer) params.get("maximumPeriodAvailableType"));
        product.setFranchisePeriod(Long.valueOf(params.get("franchisePeriod").toString()));
        product.setStatus((Integer) params.get("status"));
        product.setBuyLimit((String) params.get("buyLimit"));
        product.setAllowInstallmentPayment((Integer) params.get("allowInstallmentPayment"));
        product.setInsuranceCompanyId(Long.valueOf(params.get("insuranceCompanyId").toString()));
        product.setSuitStoreIds((String) params.get("suitStoreIds"));
        product.setSort((Integer) params.get("sort"));
        product.setCreatedBy(getUsername());

        // 从params中提取保障期限信息
        List<Map<String, Object>> termsData = (List<Map<String, Object>>) params.get("terms");
        List<ProductTerm> terms = termsData.stream().map(termData -> {
            ProductTerm term = new ProductTerm();
            term.setName((String) termData.get("name"));
            term.setTimeLimit(Long.valueOf(termData.get("timeLimit").toString()));
            term.setMileage(Long.valueOf(termData.get("mileage").toString()));
            term.setCreatedBy(getUsername());
            return term;
        }).toList();

        return toAjax(productService.saveProductWithTerms(product, terms));
    }

    /**
     * 修改产品管理
     */
    @PreAuthorize("@ss.hasPermi('yanbao:productManage:edit')")
    @Log(title = "产品管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody Map<String, Object> params)
    {
        Product product = new Product();
        // 从params中提取产品信息
        product.setId(Long.valueOf(params.get("id").toString()));
        product.setType((Integer) params.get("type"));
        product.setName((String) params.get("name"));
        product.setRemark((String) params.get("remark"));
        product.setSalesMode((Integer) params.get("salesMode"));
        product.setMaximumMileageAvailable(Long.valueOf(params.get("maximumMileageAvailable").toString()));
        product.setMaximumPeriodAvailable(Long.valueOf(params.get("maximumPeriodAvailable").toString()));
        product.setMaximumPeriodAvailableType((Integer) params.get("maximumPeriodAvailableType"));
        product.setFranchisePeriod(Long.valueOf(params.get("franchisePeriod").toString()));
        product.setStatus((Integer) params.get("status"));
        product.setBuyLimit((String) params.get("buyLimit"));
        product.setAllowInstallmentPayment((Integer) params.get("allowInstallmentPayment"));
        product.setInsuranceCompanyId(Long.valueOf(params.get("insuranceCompanyId").toString()));
        product.setSuitStoreIds((String) params.get("suitStoreIds"));
        product.setSort((Integer) params.get("sort"));
        product.setChangedBy(getUsername());

        // 从params中提取保障期限信息
        List<Map<String, Object>> termsData = (List<Map<String, Object>>) params.get("terms");
        List<ProductTerm> terms = termsData.stream().map(termData -> {
            ProductTerm term = new ProductTerm();
            term.setName((String) termData.get("name"));
            term.setTimeLimit(Long.valueOf(termData.get("timeLimit").toString()));
            term.setMileage(Long.valueOf(termData.get("mileage").toString()));
            term.setCreatedBy(getUsername());
            return term;
        }).toList();

        return toAjax(productService.updateProductWithTerms(product, terms));
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('yanbao:productManage:edit')")
    @Log(title = "产品管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody Product product)
    {
        return toAjax(productService.changeStatus(product.getId(), product.getStatus()));
    }

    /**
     * 获取生效的产品类型列表
     */
    @GetMapping("/activeProductTypes")
    public AjaxResult getActiveProductTypes()
    {
        List<ProductType> productTypes = productTypeService.selectActiveProductTypes();
        return success(productTypes);
    }
}
