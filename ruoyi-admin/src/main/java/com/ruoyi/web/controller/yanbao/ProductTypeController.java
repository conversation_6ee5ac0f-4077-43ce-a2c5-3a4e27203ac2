package com.ruoyi.web.controller.yanbao;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.yanbao.entity.ProductType;
import com.ruoyi.yanbao.service.ProductTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 产品类型管理Controller
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@RestController
@RequestMapping("/yanbao/productType")
public class ProductTypeController extends BaseController {
    @Autowired
    private ProductTypeService productTypeService;

    /**
     * 查询产品类型管理列表
     */
    @PreAuthorize("@ss.hasPermi('yanbao:productType:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProductType productType) {
        startPage();
        List<ProductType> list = productTypeService.selectProductTypeList(productType);
        return getDataTable(list);
    }

    /**
     * 获取产品类型管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('yanbao:productType:edit')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(productTypeService.getById(id));
    }

    /**
     * 修改产品类型管理
     */
    @PreAuthorize("@ss.hasPermi('yanbao:productType:edit')")
    @Log(title = "产品类型管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody ProductType productType) {
        productType.setChangedBy(getUsername());
        return toAjax(productTypeService.updateById(productType));
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('yanbao:productType:edit')")
    @Log(title = "产品类型管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody ProductType productType) {
        return toAjax(productTypeService.changeStatus(productType.getId(), productType.getStatus()));
    }

}
