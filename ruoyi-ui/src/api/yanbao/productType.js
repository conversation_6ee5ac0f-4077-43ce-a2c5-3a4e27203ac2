import request from '@/utils/request.js'

// 查询产品类型管理列表
export function listProductType(query) {
  return request({
    url: '/yanbao/productType/list',
    method: 'get',
    params: query
  })
}

// 查询产品类型管理详细
export function getProductType(id) {
  return request({
    url: '/yanbao/productType/' + id,
    method: 'get'
  })
}

// 修改产品类型管理
export function updateProductType(data) {
  return request({
    url: '/yanbao/productType',
    method: 'put',
    data: data
  })
}

// 产品类型状态修改
export function changeProductTypeStatus(id, status) {
  const data = {
    id,
    status
  }
  return request({
    url: '/yanbao/productType/changeStatus',
    method: 'put',
    data: data
  })
}
