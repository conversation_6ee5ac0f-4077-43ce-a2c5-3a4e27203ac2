import request from '@/utils/request.js'

// 查询产品管理列表
export function listProduct(query) {
  return request({
    url: '/yanbao/productManage/list',
    method: 'get',
    params: query
  })
}

// 查询产品管理详细
export function getProduct(id) {
  return request({
    url: '/yanbao/productManage/' + id,
    method: 'get'
  })
}

// 新增产品管理
export function addProduct(data) {
  return request({
    url: '/yanbao/productManage',
    method: 'post',
    data: data
  })
}

// 修改产品管理
export function updateProduct(data) {
  return request({
    url: '/yanbao/productManage',
    method: 'put',
    data: data
  })
}

// 产品状态修改
export function changeProductStatus(id, status) {
  const data = {
    id,
    status
  }
  return request({
    url: '/yanbao/productManage/changeStatus',
    method: 'put',
    data: data
  })
}

// 获取生效的产品类型列表
export function getActiveProductTypes() {
  return request({
    url: '/yanbao/productManage/activeProductTypes',
    method: 'get'
  })
}
