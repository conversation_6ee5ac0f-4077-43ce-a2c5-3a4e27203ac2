<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="保单号" prop="insuranceNo">
        <el-input
            v-model="queryParams.insuranceNo"
            placeholder="请输入保单号"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车主姓名" prop="customerName">
        <el-input
            v-model="queryParams.customerName"
            placeholder="请输入车主姓名"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="联系电话" prop="contactTelephone">
        <el-input
            v-model="queryParams.contactTelephone"
            placeholder="请输入联系电话"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车架号" prop="vinNo">
        <el-input
            v-model="queryParams.vinNo"
            placeholder="请输入车架号"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车牌号" prop="carNo">
        <el-input
            v-model="queryParams.carNo"
            placeholder="请输入车牌号"
            clearable
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="投保状态" prop="insuranceState">
        <el-select style="width: 200px" v-model="queryParams.insuranceState" placeholder="请选择投保状态" clearable>
          <el-option label="待投保" :value="0" />
          <el-option label="已投保" :value="1" />
          <el-option label="退保" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="订单状态" prop="orderState">
        <el-select style="width: 200px" v-model="queryParams.orderState" placeholder="请选择订单状态" clearable>
          <el-option label="暂存" :value="0" />
          <el-option label="待审核" :value="1" />
          <el-option label="驳回" :value="2" />
          <el-option label="审核通过" :value="3" />
        </el-select>
      </el-form-item>
      <el-form-item label="业务员" prop="saleUserId">
        <el-select style="width: 200px" v-model="queryParams.saleUserId" placeholder="请选择业务员" clearable>
          <el-option v-for="item in users" :key="item.saleUserId" :label="item.saleUserName" :value="item.saleUserId"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="经销商" prop="storeId">
        <el-select style="width: 200px" v-model="queryParams.storeId" placeholder="请选择经销商" clearable>
          <el-option v-for="item in stores" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="产品" prop="productId">
        <el-select style="width: 200px" v-model="queryParams.productId" placeholder="请选择产品" clearable>
          <el-option v-for="item in products" :key="item.productId" :label="item.productName" :value="item.productId"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="保险公司" prop="insuranceCompanyId">
        <el-select style="width: 200px" v-model="queryParams.insuranceCompanyId" placeholder="请选择保险公司" clearable>
          <el-option v-for="item in companys" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="info" plain icon="Upload" @click="handleImport" v-hasPermi="['yanbao:insurance:add']">批量投保</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="orderList" size="small">
      <el-table-column label="订单号" fixed align="center" prop="orderNo" width="100">
        <template #default="scope">
          <el-link type="primary" @click="handleDetail(scope.row)">{{ scope.row.productOrderId }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="保单号" align="center" prop="insuranceNo" width="140"/>
      <el-table-column label="保险公司" align="center" prop="insuranceCompanyName" width="140"/>
      <el-table-column label="车牌号" align="center" prop="carNo" width="100"/>
      <el-table-column label="车架号" align="center" prop="vinNo" width="140"/>
      <el-table-column label="产品名称" align="center" prop="productName" width="150"/>
      <el-table-column label="车主姓名" align="center" prop="customerName" width="120" />
      <el-table-column label="保单生效时间" align="center" prop="beginTime" width="120">
        <template #default="scope">
          <span>{{ parseTime(scope.row.beginTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="保单截止时间" align="center" prop="endTime" width="120">
        <template #default="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="保单状态" align="center" prop="insuranceState" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.insuranceState==0" :disable-transitions="true" :key="0">待投保</el-tag>
          <el-tag v-else-if="scope.row.insuranceState==1" :disable-transitions="true" :key="1"  type="success">已投保</el-tag>
          <el-tag v-else-if="scope.row.insuranceState==2" :disable-transitions="true" :key="2" type="warning">退保</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="订单状态" align="center" prop="orderState" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.orderState==0" :disable-transitions="true" :key="0" type="info">暂存</el-tag>
          <el-tag v-else-if="scope.row.orderState==1" :disable-transitions="true" :key="1">待审核</el-tag>
          <el-tag v-else-if="scope.row.orderState==2" :disable-transitions="true" :key="2" type="danger">驳回</el-tag>
          <el-tag v-else-if="scope.row.orderState==3" :disable-transitions="true" :key="3" type="success">审核通过</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" align="center" class-name="small-padding fixed-width" min-width="150">
        <template #default="scope">
          <el-button v-if="(scope.row.insuranceState==0||scope.row.insuranceState==2)&&scope.row.orderState==3" link type="primary" @click="handleInsurance(scope.row)" v-hasPermi="['yanbao:insurance:add']">投保</el-button>
          <el-button v-if="scope.row.insuranceState==1" link type="primary" @click="handleCancelInsurance(scope.row)" v-hasPermi="['yanbao:insurance:cancel']">退保</el-button>
          <el-button v-if="scope.row.insuranceState==1||scope.row.insuranceState==2" link type="info" @click="handleView(scope.row)" v-hasPermi="['yanbao:insurance:query']">查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 投保表单 -->
    <InsuranceForm ref="insuranceFormRef" @success="getList" />

    <!-- 投保导入对话框 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload ref="uploadRef" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" :action="upload.url" :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline" @click="importTemplate">下载模板</el-link>
          </div>
        </template>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="OrderList">
import { getAllSaleUser,getAllProduct} from "@/api/yanbao/productOrder"
import {optionSelectInsuranceCompany } from "@/api/yanbao/insuranceCompany"
import { list, cancelInsurance } from "@/api/yanbao/insurance"
import {optionSelectStore} from "@/api/yanbao/store";
import { parseTime } from '@/utils/ruoyi';
import { getToken } from "@/utils/auth"
import InsuranceForm from './components/InsuranceForm.vue';
const { proxy } = getCurrentInstance()
const router = useRouter()

const orderList = ref([])
const loading = ref(true)
const showSearch = ref(true)
const total = ref(0)
const users = ref([])
const stores = ref([])
const products= ref([])
const companys= ref([])

// 投保表单引用
const insuranceFormRef = ref()

/*** 投保导入参数 */
const upload = reactive({
  // 是否显示弹出层（投保导入）
  open: false,
  // 弹出层标题（投保导入）
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的投保数据
  // 设置上传的请求头部
  headers: { Authorization: "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/yanbao/insurance/importData"
})

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    orderNo: null,
    customerName: null,
    contactTelephone: null
  }
})

const { queryParams } = toRefs(data)

/** 查询订单列表 */
function getList() {
  loading.value = true
  list(queryParams.value).then(response => {
    orderList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 查看详情按钮操作 */
function handleDetail(row) {
  router.push({
    path: "/yanbao/order/detail",
    query: { orderId: row.productOrderId }
  })
}
/** 投保操作 */
function handleInsurance(row) {
  insuranceFormRef.value.openForm(row, false)
}

/** 退保操作 */
function handleCancelInsurance(row) {
  proxy.$modal.confirm('是否确认退保该保单？').then(function() {
    return cancelInsurance(row.id)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("退保成功")
  }).catch(() => {})
}

/** 查看投保信息 */
function handleView(row) {
  insuranceFormRef.value.openForm(row, true)
}

/** 导入按钮操作 */
function handleImport() {
  upload.title = "投保导入"
  upload.open = true
}

/** 下载模板操作 */
function importTemplate() {
  proxy.download("yanbao/insurance/importTemplate", {
    ...queryParams.value
  }, `insurance_template_${new Date().getTime()}.xlsx`)
}

/** 文件上传中处理 */
function handleFileUploadProgress(event, file, fileList) {
  upload.isUploading = true
}

/** 文件上传成功处理 */
function handleFileSuccess(response, file, fileList) {
  upload.open = false
  upload.isUploading = false
  proxy.$refs["uploadRef"].handleRemove(file)
  proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true })
  getList()
}

/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit()
}

getList()

getAllSaleUser().then(resp=>{
  users.value = resp.data;
})
optionSelectStore().then(resp=>{
  stores.value=resp.data;
})
getAllProduct().then(resp=>{
  products.value=resp.data;
})
optionSelectInsuranceCompany({}).then(resp=>{
  companys.value=resp.data;
})
</script>
