<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="类型名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入类型名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" style="width: 200px" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in statusOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="productTypeList">
      <el-table-column label="业务类型" align="center" prop="type" width="120" />
      <el-table-column label="类型名称" align="center" prop="name" :show-overflow-tooltip="true" />
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template #default="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            @change="handleStatusChange(scope.row)"
            v-hasPermi="['yanbao:productType:edit']"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="160">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['yanbao:productType:edit']">修改</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改产品类型管理对话框 -->
    <el-dialog :title="title" v-model="open" width="700px" append-to-body>
      <el-form ref="productTypeRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="业务类型" prop="type">
          <el-input disabled v-model="form.type" placeholder="请输入业务类型" />
        </el-form-item>
        <el-form-item label="类型名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入类型名称" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in statusOptions"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="前端配置" prop="frontSetting">
          <el-input v-model="form.frontSetting" :rows="5" type="textarea" placeholder="请输入前端配置JSON" />
        </el-form-item>
        <el-form-item label="后端配置" prop="backendSetting">
          <el-input v-model="form.backendSetting" :rows="5" type="textarea" placeholder="请输入后端配置JSON" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ProductType">
import { listProductType, getProductType, updateProductType, changeProductTypeStatus } from "@/api/yanbao/productType.js"

const { proxy } = getCurrentInstance()

const productTypeList = ref([])
const statusOptions = ref([
  { label: '有效', value: 1 },
  { label: '无效', value: 0 }
])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    type: null,
    name: null,
    status: null
  },
  rules: {
    name: [
      { required: true, message: "类型名称不能为空", trigger: "blur" }
    ],
    status: [
      { required: true, message: "状态不能为空", trigger: "change" }
    ]
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询产品类型管理列表 */
function getList() {
  loading.value = true
  listProductType(queryParams.value).then(response => {
    productTypeList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    type: null,
    name: null,
    status: 1,
    frontSetting: null,
    backendSetting: null
  }
  proxy.resetForm("productTypeRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const id = row.id || ids.value
  getProductType(id).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改产品类型"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["productTypeRef"].validate(valid => {
    if (valid) {
      updateProductType(form.value).then(response => {
        proxy.$modal.msgSuccess("修改成功")
        open.value = false
        getList()
      })
    }
  })
}

/** 状态修改 */
function handleStatusChange(row) {
  let text = row.status === 1 ? "启用" : "停用"
  proxy.$modal.confirm('确认要"' + text + '""' + row.name + '"产品类型吗？').then(function() {
    return changeProductTypeStatus(row.id, row.status)
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功")
  }).catch(function() {
    row.status = row.status === 0 ? 1 : 0
  })
}

getList()
</script>
