<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="产品名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入产品名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="产品类型" prop="type">
        <el-select style="width: 200px" v-model="queryParams.type" placeholder="请选择产品类型" clearable>
          <el-option
            v-for="item in productTypeOptions"
            :key="item.type"
            :label="item.name"
            :value="item.type"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="销售模式" prop="salesMode">
        <el-select style="width: 200px" v-model="queryParams.salesMode" placeholder="请选择销售模式" clearable>
          <el-option
            v-for="dict in salesModeOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select style="width: 200px" v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in statusOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['yanbao:productManage:add']"
        >新增</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="productList">
      <el-table-column label="产品名称" align="center" prop="name" :show-overflow-tooltip="true" />
      <el-table-column label="产品类型" align="center" prop="type" width="100">
        <template #default="scope">
          <span>{{ getProductTypeName(scope.row.type) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="销售模式" align="center" prop="salesMode" width="100">
        <template #default="scope">
          <span v-if="scope.row.salesMode === 1">自营</span>
          <span v-else-if="scope.row.salesMode === 2">代销</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template #default="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            @change="handleStatusChange(scope.row)"
            v-hasPermi="['yanbao:productManage:edit']"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="sort" width="80" />
      <el-table-column label="创建时间" align="center" prop="createdAt" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="160">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['yanbao:productManage:edit']">修改</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改产品管理对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="productRef" :model="form" :rules="rules" label-width="120px">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="基本信息" name="basic">
            <el-row>
              <el-col :span="12">
                <el-form-item label="产品名称" prop="name">
                  <el-input v-model="form.name" placeholder="请输入产品名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="产品类型" prop="type">
                  <el-select v-model="form.type" placeholder="请选择产品类型">
                    <el-option
                      v-for="item in productTypeOptions"
                      :key="item.type"
                      :label="item.name"
                      :value="item.type"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="销售模式" prop="salesMode">
                  <el-select v-model="form.salesMode" placeholder="请选择销售模式">
                    <el-option
                      v-for="dict in salesModeOptions"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="保险公司" prop="insuranceCompanyId">
                  <el-select v-model="form.insuranceCompanyId" placeholder="请选择保险公司">
                    <el-option
                      v-for="item in insuranceCompanyOptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="最大里程数" prop="maximumMileageAvailable">
                  <el-input-number v-model="form.maximumMileageAvailable" :min="0" :step="1000" controls-position="right" placeholder="请输入最大里程数" />
                  <span class="ml5">公里</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="最大期限" prop="maximumPeriodAvailable">
                  <el-input-number v-model="form.maximumPeriodAvailable" :min="0" controls-position="right" placeholder="请输入最大期限" />
                  <el-select v-model="form.maximumPeriodAvailableType" style="width: 80px">
                    <el-option label="月" :value="1" />
                    <el-option label="日" :value="2" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="免赔期" prop="franchisePeriod">
                  <el-input-number v-model="form.franchisePeriod" :min="0" controls-position="right" placeholder="请输入免赔期" />
                  <span class="ml5">天</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="允许分期" prop="allowInstallmentPayment">
                  <el-radio-group v-model="form.allowInstallmentPayment">
                    <el-radio :label="1">允许</el-radio>
                    <el-radio :label="0">不允许</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="状态" prop="status">
                  <el-radio-group v-model="form.status">
                    <el-radio
                      v-for="dict in statusOptions"
                      :key="dict.value"
                      :label="dict.value"
                    >{{dict.label}}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="排序" prop="sort">
                  <el-input-number v-model="form.sort" :min="0" controls-position="right" placeholder="请输入排序值" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="适用门店" prop="suitStoreIds">
              <el-select v-model="form.suitStoreIds" multiple placeholder="请选择适用门店，不选则适用全部门店">
                <el-option
                  v-for="item in storeOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id.toString()"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="购买限制" prop="buyLimit">
              <el-input v-model="form.buyLimit" type="textarea" placeholder="请输入购买限制" />
            </el-form-item>
            <el-form-item label="产品说明" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入产品说明" />
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="保障期限" name="terms">
            <el-button type="primary" @click="handleAddTerm" style="margin-bottom: 10px">添加保障期限</el-button>
            <el-table :data="form.terms" style="width: 100%">
              <el-table-column label="期限名称" align="center" prop="name">
                <template #default="scope">
                  <el-input v-model="scope.row.name" placeholder="请输入期限名称" />
                </template>
              </el-table-column>
              <el-table-column label="期限(月)" align="center" prop="timeLimit">
                <template #default="scope">
                  <el-input-number v-model="scope.row.timeLimit" :min="0" controls-position="right" placeholder="请输入期限" />
                </template>
              </el-table-column>
              <el-table-column label="里程(公里)" align="center" prop="mileage">
                <template #default="scope">
                  <el-input-number v-model="scope.row.mileage" :min="0" :step="1000" controls-position="right" placeholder="请输入里程" />
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="100">
                <template #default="scope">
                  <el-button type="danger" link icon="Delete" @click="handleDeleteTerm(scope.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ProductManage">
import { listProduct, getProduct, addProduct, updateProduct, changeProductStatus, getActiveProductTypes } from "@/api/yanbao/productManage.js"
import { optionSelectInsuranceCompany } from "@/api/yanbao/insuranceCompany.js"
import { optionSelectStore } from "@/api/yanbao/store.js"

const { proxy } = getCurrentInstance()

const productList = ref([])
const productTypeOptions = ref([])
const insuranceCompanyOptions = ref([])
const storeOptions = ref([])
const salesModeOptions = ref([
  { label: '自营', value: 1 },
  { label: '代销', value: 2 }
])
const statusOptions = ref([
  { label: '有效', value: 1 },
  { label: '无效', value: 0 }
])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const activeTab = ref('basic')

const data = reactive({
  form: {
    id: null,
    type: null,
    name: '',
    remark: '',
    salesMode: 1,
    maximumMileageAvailable: 0,
    maximumPeriodAvailable: 0,
    maximumPeriodAvailableType: 1,
    franchisePeriod: 0,
    status: 1,
    buyLimit: '',
    allowInstallmentPayment: 1,
    insuranceCompanyId: null,
    suitStoreIds: [],
    sort: 0,
    terms: []
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: null,
    type: null,
    salesMode: null,
    status: null
  },
  rules: {
    name: [
      { required: true, message: "产品名称不能为空", trigger: "blur" }
    ],
    type: [
      { required: true, message: "产品类型不能为空", trigger: "change" }
    ],
    salesMode: [
      { required: true, message: "销售模式不能为空", trigger: "change" }
    ],
    insuranceCompanyId: [
      { required: true, message: "保险公司不能为空", trigger: "change" }
    ],
    status: [
      { required: true, message: "状态不能为空", trigger: "change" }
    ]
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询产品管理列表 */
function getList() {
  loading.value = true
  listProduct(queryParams.value).then(response => {
    productList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 获取产品类型名称 */
function getProductTypeName(type) {
  const productType = productTypeOptions.value.find(item => item.type === type)
  return productType ? productType.name : ''
}

/** 获取生效的产品类型 */
function getProductTypes() {
  getActiveProductTypes().then(response => {
    productTypeOptions.value = response.data
  })
}

/** 获取保险公司列表 */
function getInsuranceCompanies() {
  optionSelectInsuranceCompany(1).then(response => {
    insuranceCompanyOptions.value = response.data
  })
}

/** 获取门店列表 */
function getStores() {
  optionSelectStore(1).then(response => {
    storeOptions.value = response.data
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    type: null,
    name: '',
    remark: '',
    salesMode: 1,
    maximumMileageAvailable: 0,
    maximumPeriodAvailable: 0,
    maximumPeriodAvailableType: 1,
    franchisePeriod: 0,
    status: 1,
    buyLimit: '',
    allowInstallmentPayment: 1,
    insuranceCompanyId: null,
    suitStoreIds: [],
    sort: 0,
    terms: []
  }
  activeTab.value = 'basic'
  proxy.resetForm("productRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加产品"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const id = row.id || ids.value
  getProduct(id).then(response => {
    form.value = response.data
    // 处理suitStoreIds
    if (form.value.suitStoreIds) {
      form.value.suitStoreIds = form.value.suitStoreIds.split(',')
    } else {
      form.value.suitStoreIds = []
    }
    open.value = true
    title.value = "修改产品"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["productRef"].validate(valid => {
    if (valid) {
      // 处理suitStoreIds
      const formData = { ...form.value }
      if (formData.suitStoreIds && formData.suitStoreIds.length > 0) {
        formData.suitStoreIds = formData.suitStoreIds.join(',')
      } else {
        formData.suitStoreIds = ''
      }

      if (form.value.id != null) {
        updateProduct(formData).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addProduct(formData).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 状态修改 */
function handleStatusChange(row) {
  let text = row.status === 1 ? "启用" : "停用"
  proxy.$modal.confirm('确认要"' + text + '""' + row.name + '"产品吗？').then(function() {
    return changeProductStatus(row.id, row.status)
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功")
  }).catch(function() {
    row.status = row.status === 0 ? 1 : 0
  })
}

/** 添加保障期限 */
function handleAddTerm() {
  form.value.terms.push({
    name: '',
    timeLimit: 0,
    mileage: 0
  })
}

/** 删除保障期限 */
function handleDeleteTerm(index) {
  form.value.terms.splice(index, 1)
}

getList()
getProductTypes()
getInsuranceCompanies()
getStores()
</script>

<style scoped>
.ml5 {
  margin-left: 5px;
}
</style>
