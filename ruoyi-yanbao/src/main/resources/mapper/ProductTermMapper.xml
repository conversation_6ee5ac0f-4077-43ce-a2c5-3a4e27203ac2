<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.yanbao.mapper.ProductTermMapper">

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO p_product_term (product_id, name, time_limit, mileage, is_delete, created_at, created_by)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.productId}, #{item.name}, #{item.timeLimit}, #{item.mileage}, 0, NOW(), #{item.createdBy})
        </foreach>
    </insert>

</mapper>
