package com.ruoyi.yanbao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.exception.base.BaseException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.OssUtil;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.SysFile;
import com.ruoyi.system.service.ISysFileService;
import com.ruoyi.yanbao.entity.Product;
import com.ruoyi.yanbao.entity.ProductOrder;
import com.ruoyi.yanbao.entity.ProductOrderInsurance;
import com.ruoyi.yanbao.entity.vo.ProductOrderInsuranceVo;
import com.ruoyi.yanbao.mapper.ProductMapper;
import com.ruoyi.yanbao.mapper.ProductOrderInsuranceMapper;
import com.ruoyi.yanbao.mapper.ProductOrderMapper;
import com.ruoyi.yanbao.service.ProductOrderInsuranceService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 投保管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Service
public class ProductOrderInsuranceServiceImpl extends ServiceImpl<ProductOrderInsuranceMapper, ProductOrderInsurance> implements ProductOrderInsuranceService {

    @Autowired
    private ProductOrderMapper productOrderMapper;

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private ISysFileService sysFileService;

    @Override
    public ProductOrderInsurance push(Long productOrderId, String createdBy) {
        if (productOrderId != null) {
            ProductOrderInsurance productOrderInsurance = baseMapper.selectOne(new LambdaQueryWrapper<ProductOrderInsurance>()
                    .eq(ProductOrderInsurance::getProductOrderId, productOrderId));
            if (productOrderInsurance != null) {
                return productOrderInsurance;
            }

            ProductOrder order = productOrderMapper.selectById(productOrderId);
            if (order != null) {
                Product product = productMapper.selectById(order.getProductId());
                if (product.getInsuranceCompanyId() != null) {
                    ProductOrderInsurance item = new ProductOrderInsurance();
                    item.setCreatedAt(new Date());
                    item.setCreatedBy(StringUtils.isNotBlank(createdBy) ? createdBy : SecurityUtils.getLoginUser().getUsername());
                    item.setIsDelete(Constants.NOT_DELETE);
                    item.setProductOrderId(productOrderId);
                    item.setInsuranceCompanyId(product.getInsuranceCompanyId());
                    item.setInsuranceState(ProductOrderInsuranceVo.InsuranceState.WAIT);
                    save(item);
                    return item;
                }
            }
        }
        return null;
    }

    @Override
    public List<ProductOrderInsuranceVo> queryList(ProductOrderInsuranceVo param) {
        return baseMapper.queryList(param);
    }

    @Override
    @Transactional
    public boolean doInsurance(ProductOrderInsuranceVo param) {
        // 参数验证
        if (param.getId() == null) {
            throw new BaseException("投保记录ID不能为空");
        }
        if (StringUtils.isBlank(param.getInsuranceNo())) {
            throw new BaseException("保单号不能为空");
        }
        if (param.getInsuranceDate() == null) {
            throw new BaseException("投保时间不能为空");
        }
        if (param.getInsuranceEnableRangeDate() == null) {
            throw new BaseException("保险生效日期不能为空");
        }
        if (param.getPrice() == null) {
            throw new BaseException("保险费用不能为空");
        }

        // 获取投保记录
        ProductOrderInsurance insurance = getById(param.getId());
        if (insurance == null) {
            throw new BaseException("投保记录不存在");
        }

        // 检查投保状态
        if (!ProductOrderInsuranceVo.InsuranceState.WAIT.equals(insurance.getInsuranceState())
                && !ProductOrderInsuranceVo.InsuranceState.CANCEL.equals(insurance.getInsuranceState())) {
            throw new BaseException("当前状态不允许投保");
        }

        // 检查保单号是否重复
        LambdaQueryWrapper<ProductOrderInsurance> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductOrderInsurance::getInsuranceNo, param.getInsuranceNo())
                .ne(ProductOrderInsurance::getId, param.getId());
        if (count(wrapper) > 0) {
            throw new BaseException("保单号已存在");
        }

        // 更新投保信息
        insurance.setInsuranceState(ProductOrderInsuranceVo.InsuranceState.INSURED);
        insurance.setInsuranceNo(param.getInsuranceNo());
        insurance.setInsuranceDate(param.getInsuranceDate());
        insurance.setBeginTime(DateUtils.getBenginTime(param.getInsuranceEnableRangeDate()[0]));
        insurance.setEndTime(DateUtils.getEndTime(param.getInsuranceEnableRangeDate()[1]));
        insurance.setPrice(param.getPrice());
        insurance.setInsuranceFile(param.getInsuranceFile());
        insurance.setChangedAt(new Date());
        insurance.setChangedBy(SecurityUtils.getLoginUser().getUsername());
        if (CollectionUtils.isNotEmpty(param.getInsuranceFileList())) {
            List<Long> fileIds = new ArrayList<>();
            for (OssUtil.FileItem fileItem : param.getInsuranceFileList()) {
                SysFile sysFile = sysFileService.selectSysFileById(fileItem.getId());
                fileIds.add(sysFile.getId());
                if (sysFile != null && sysFile.getRelatedId() == null) {
                    sysFile.setRelatedId(insurance.getId());
                    sysFile.setUpdateTime(new Date());
                    sysFile.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
                    sysFileService.updateSysFile(sysFile);
                }
            }
            insurance.setInsuranceFile(StringUtils.join(fileIds, ","));
        } else {
            insurance.setInsuranceFile("");
        }
        return updateById(insurance);
    }

    @Override
    @Transactional
    public boolean doCancelInsurance(Long id) {
        if (id == null) {
            throw new BaseException("投保记录ID不能为空");
        }

        // 获取投保记录
        ProductOrderInsurance insurance = getById(id);
        if (insurance == null) {
            throw new BaseException("投保记录不存在");
        }

        // 检查投保状态
        if (!ProductOrderInsuranceVo.InsuranceState.INSURED.equals(insurance.getInsuranceState())) {
            throw new BaseException("只有已投保状态才能退保");
        }

        // 更新为退保状态
        insurance.setInsuranceState(ProductOrderInsuranceVo.InsuranceState.CANCEL);
        insurance.setChangedAt(new Date());
        insurance.setChangedBy(SecurityUtils.getLoginUser().getUsername());

        return updateById(insurance);
    }

    @Override
    public ProductOrderInsuranceVo getInsuranceDetail(Long id) {
        if (id == null) {
            throw new BaseException("投保记录ID不能为空");
        }

        ProductOrderInsuranceVo param = new ProductOrderInsuranceVo();
        param.setId(id);
        List<ProductOrderInsuranceVo> list = baseMapper.queryList(param);

        ProductOrderInsuranceVo detail = list.isEmpty() ? null : list.get(0);
        if (detail == null) {
            throw new BaseException("投保记录不存在");
        }
        detail.setInsuranceEnableRangeDate(new Date[]{detail.getBeginTime(), detail.getEndTime()});
        if (StringUtils.isNotBlank(detail.getInsuranceFile())) {
            detail.setInsuranceFileList(getFileItemList(detail.getInsuranceFile()));
        }

        return detail;
    }

    private List<OssUtil.FileItem> getFileItemList(String fileId) {
        List<OssUtil.FileItem> fileItems = new ArrayList<>();
        if (StringUtils.isNotBlank(fileId)) {
            List<SysFile> sysFiles = sysFileService.selectSysFileByIds(StringUtils.stringToLong(fileId));
            if (CollectionUtils.isNotEmpty(sysFiles)) {
                for (SysFile sysFile : sysFiles) {
                    OssUtil.FileItem fileItem = new OssUtil.FileItem();
                    fileItem.setId(sysFile.getId());
                    fileItem.setName(sysFile.getName());
                    fileItem.setPath(sysFile.getPath());
                    fileItem.setSize(sysFile.getSize());
                    if (SecurityUtils.hasPermi("yanbao:insurance:download")) {
                        fileItem.setUrl(OssUtil.getObjectUrl(sysFile.getPath(), null, null));
                    }
                    fileItems.add(fileItem);
                }
            }
        }
        return fileItems;
    }

    @Override
    public String downloadInsuranceFile(Long id) {
        if (id == null) {
            throw new BaseException("投保记录ID不能为空");
        }

        ProductOrderInsurance insurance = getById(id);
        if (insurance == null) {
            throw new BaseException("投保记录不存在");
        }

        if (StringUtils.isBlank(insurance.getInsuranceFile())) {
            throw new BaseException("保单文件不存在");
        }

        // 获取文件信息
        SysFile sysFile = sysFileService.selectSysFileById(Long.parseLong(insurance.getInsuranceFile()));
        if (sysFile == null) {
            throw new BaseException("保单文件不存在");
        }

        return sysFile.getPath();
    }

    @Override
    @Transactional
    public String importInsurance(List<ProductOrderInsuranceVo> insuranceList, String operName) {
        if (CollectionUtils.isEmpty(insuranceList)) {
            throw new BaseException("导入数据不能为空");
        }

        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        for (ProductOrderInsuranceVo insuranceVo : insuranceList) {
            try {
                ProductOrderInsurance existInsurance = baseMapper.selectOne(new LambdaQueryWrapper<ProductOrderInsurance>()
                        .eq(ProductOrderInsurance::getProductOrderId, insuranceVo.getProductOrderId()));
                if (existInsurance == null) {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、订单号 ").append(insuranceVo.getProductOrderId()).append(" 对应的投保记录不存在");
                    continue;
                }
                // 检查是否已投保
                if (ProductOrderInsuranceVo.InsuranceState.INSURED.equals(existInsurance.getInsuranceState())) {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、订单号 ").append(insuranceVo.getProductOrderId()).append(" 已投保，不允许重复投保");
                    continue;
                }

                // 验证必填字段
                if (StringUtils.isBlank(insuranceVo.getInsuranceNo())) {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、订单号 ").append(insuranceVo.getProductOrderId()).append(" 保单号不能为空");
                    continue;
                }

                if (insuranceVo.getInsuranceDate() == null) {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、订单号 ").append(insuranceVo.getProductOrderId()).append(" 投保时间不能为空");
                    continue;
                }

                if (insuranceVo.getBeginTime() == null || insuranceVo.getEndTime() == null) {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、订单号 ").append(insuranceVo.getProductOrderId()).append(" 保险生效日期不能为空");
                    continue;
                }

                if (insuranceVo.getPrice() == null) {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、订单号 ").append(insuranceVo.getProductOrderId()).append(" 投保金额不能为空");
                    continue;
                }
                Set<String> nos = new HashSet<>();
                // 检查保单号是否重复
                LambdaQueryWrapper<ProductOrderInsurance> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(ProductOrderInsurance::getInsuranceNo, insuranceVo.getInsuranceNo())
                        .ne(ProductOrderInsurance::getId, existInsurance.getId());
                if (count(wrapper) > 0 || nos.contains(insuranceVo.getInsuranceNo())) {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、订单号 ").append(insuranceVo.getProductOrderId()).append(" 保单号已存在");
                    continue;
                }

                // 执行投保操作
                ProductOrderInsurance insurance = getById(existInsurance.getId());
                insurance.setInsuranceState(ProductOrderInsuranceVo.InsuranceState.INSURED);
                insurance.setInsuranceNo(insuranceVo.getInsuranceNo());
                nos.add(insuranceVo.getInsuranceNo());
                insurance.setInsuranceDate(insuranceVo.getInsuranceDate());
                insurance.setBeginTime(DateUtils.getBenginTime(insuranceVo.getBeginTime()));
                insurance.setEndTime(DateUtils.getEndTime(insuranceVo.getEndTime()));
                insurance.setPrice(insuranceVo.getPrice());
                insurance.setInsuranceFile(""); // 批量导入时保单文件为空
                insurance.setChangedAt(new Date());
                insurance.setChangedBy(operName);
                updateById(insurance);
                successNum++;
                successMsg.append("<br/>").append(successNum).append("、订单号 ").append(insuranceVo.getProductOrderId()).append(" 投保成功");

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、订单号 " + insuranceVo.getProductOrderId() + " 导入失败：";
                failureMsg.append(msg).append(e.getMessage());
            }
        }

        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new BaseException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
