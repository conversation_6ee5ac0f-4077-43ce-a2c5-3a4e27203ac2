package com.ruoyi.yanbao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.yanbao.entity.ProductType;
import com.ruoyi.yanbao.mapper.ProductTypeMapper;
import com.ruoyi.yanbao.service.ProductTypeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 产品类型表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Service
public class ProductTypeServiceImpl extends ServiceImpl<ProductTypeMapper, ProductType> implements ProductTypeService {

    @Override
    public List<ProductType> selectProductTypeList(ProductType productType) {
        LambdaQueryWrapper<ProductType> queryWrapper = new LambdaQueryWrapper<>();

        if (productType.getType() != null) {
            queryWrapper.eq(ProductType::getType, productType.getType());
        }
        if (StringUtils.isNotEmpty(productType.getName())) {
            queryWrapper.like(ProductType::getName, productType.getName());
        }
        if (productType.getStatus() != null) {
            queryWrapper.eq(ProductType::getStatus, productType.getStatus());
        }

        queryWrapper.orderByAsc(ProductType::getType);
        return this.list(queryWrapper);
    }

    @Override
    public boolean changeStatus(Long id, Integer status) {
        ProductType productType = new ProductType();
        productType.setId(id);
        productType.setStatus(status);
        return this.updateById(productType);
    }

}
