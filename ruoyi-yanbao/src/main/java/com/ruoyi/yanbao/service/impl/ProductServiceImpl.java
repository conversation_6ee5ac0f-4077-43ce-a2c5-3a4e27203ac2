package com.ruoyi.yanbao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.yanbao.entity.Product;
import com.ruoyi.yanbao.entity.ProductTerm;
import com.ruoyi.yanbao.entity.ProductType;
import com.ruoyi.yanbao.entity.vo.ProductVo;
import com.ruoyi.yanbao.mapper.ProductMapper;
import com.ruoyi.yanbao.mapper.ProductTermMapper;
import com.ruoyi.yanbao.mapper.ProductTypeMapper;
import com.ruoyi.yanbao.service.ProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 产品表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Service
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements ProductService {

    @Autowired
    private ProductTermMapper productTermMapper;

    @Autowired
    private ProductTypeMapper productTypeMapper;

    @Override
    public List<Product> getAvailableProducts(String storeIds) {
        LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Product::getStatus, Constants.STATUS_ENABLE);
        queryWrapper.select(Product::getId, Product::getName, Product::getRemark, Product::getSuitStoreIds,
                Product::getType);
        queryWrapper.orderByDesc(Product::getSort);
        List<Product> products = list(queryWrapper);
        if (StringUtils.isNotEmpty(storeIds)) {
            List<String> storeIdList = Arrays.asList(storeIds.split(","));
            products = products.stream().filter(p -> {
                if (StringUtils.isNotEmpty(p.getSuitStoreIds())) {
                    String[] storeIdArray = p.getSuitStoreIds().split(",");
                    for (String storeId : storeIdArray) {
                        if (storeIdList.contains(storeId)) {
                            return true;
                        }
                    }
                    return false;
                }
                return true;
            }).collect(Collectors.toList());
        }
        return products;
    }


    @Override
    public ProductVo getProductInfo(Long productId) {
        if (productId == null) {
            return null;
        }
        Product product = getById(productId);
        if (product == null || !Constants.STATUS_ENABLE.equals(product.getStatus())) {
            return null;
        }
        ProductVo productVo = new ProductVo();
        productVo.setId(product.getId());
        productVo.setName(product.getName());
        productVo.setRemark(product.getRemark());
        productVo.setType(product.getType());
        productVo.setAllowInstallmentPayment(product.getAllowInstallmentPayment());
        List<ProductTerm> productTerms =
                productTermMapper.selectList(new LambdaQueryWrapper<ProductTerm>().eq(ProductTerm::getProductId, productId));
        productVo.setTerms(productTerms);
        productVo.setProductType(productTypeMapper.selectOne(new LambdaQueryWrapper<ProductType>().eq(ProductType::getType, product.getType())));
        return productVo;
    }

    @Override
    public List<Product> selectProductList(Product product) {
        LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>();

        if (product.getType() != null) {
            queryWrapper.eq(Product::getType, product.getType());
        }
        if (StringUtils.isNotEmpty(product.getName())) {
            queryWrapper.like(Product::getName, product.getName());
        }
        if (product.getStatus() != null) {
            queryWrapper.eq(Product::getStatus, product.getStatus());
        }
        if (product.getSalesMode() != null) {
            queryWrapper.eq(Product::getSalesMode, product.getSalesMode());
        }
        if (product.getInsuranceCompanyId() != null) {
            queryWrapper.eq(Product::getInsuranceCompanyId, product.getInsuranceCompanyId());
        }

        queryWrapper.orderByDesc(Product::getSort).orderByDesc(Product::getCreatedAt);
        return this.list(queryWrapper);
    }

    @Override
    public boolean changeStatus(Long id, Integer status) {
        Product product = new Product();
        product.setId(id);
        product.setStatus(status);
        return this.updateById(product);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveProductWithTerms(Product product, List<ProductTerm> terms) {
        // 保存产品
        boolean result = this.save(product);
        if (result && terms != null && !terms.isEmpty()) {
            // 设置产品ID并保存保障期限
            for (ProductTerm term : terms) {
                term.setProductId(product.getId());
            }
            result = productTermMapper.insertBatch(terms) == terms.size();
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateProductWithTerms(Product product, List<ProductTerm> terms) {
        // 更新产品
        boolean result = this.updateById(product);
        if (result) {
            // 删除原有的保障期限
            productTermMapper.delete(new LambdaQueryWrapper<ProductTerm>().eq(ProductTerm::getProductId, product.getId()));
            // 保存新的保障期限
            if (terms != null && !terms.isEmpty()) {
                for (ProductTerm term : terms) {
                    term.setId(null); // 清空ID，作为新记录插入
                    term.setProductId(product.getId());
                }
                result = productTermMapper.insertBatch(terms) == terms.size();
            }
        }
        return result;
    }
}
