package com.ruoyi.yanbao.service;

import com.ruoyi.yanbao.entity.ProductType;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 产品类型表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface ProductTypeService extends IService<ProductType> {

    /**
     * 查询产品类型列表
     *
     * @param productType 产品类型
     * @return 产品类型集合
     */
    List<ProductType> selectProductTypeList(ProductType productType);

    /**
     * 修改产品类型状态
     *
     * @param id 产品类型ID
     * @param status 状态
     * @return 结果
     */
    boolean changeStatus(Long id, Integer status);

}
