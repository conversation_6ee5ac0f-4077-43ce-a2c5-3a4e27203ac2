package com.ruoyi.yanbao.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 产品类型表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@TableName("p_product_type")
public class ProductType implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Excel(name = "ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 是否删除（0：未删除，1：已删除）
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer isDelete;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private Date createdAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date changedAt;

    /**
     * 修改人
     */
    private String changedBy;

    /**
     * 业务类型
     */
    @Excel(name = "业务类型")
    private Integer type;

    /**
     * 业务类型名称
     */
    @Excel(name = "类型名称")
    private String name;

    /**
     * 前端配置JSON
     */
    private String frontSetting;

    /**
     * 后端配置JSON
     */
    private String backendSetting;

    /**
     * 状态（1：有效，0：无效）
     */
    @Excel(name = "状态", readConverterExp = "0=无效,1=有效")
    private Integer status;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getChangedAt() {
        return changedAt;
    }

    public void setChangedAt(Date changedAt) {
        this.changedAt = changedAt;
    }

    public String getChangedBy() {
        return changedBy;
    }

    public void setChangedBy(String changedBy) {
        this.changedBy = changedBy;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFrontSetting() {
        return frontSetting;
    }

    public void setFrontSetting(String frontSetting) {
        this.frontSetting = frontSetting;
    }

    public String getBackendSetting() {
        return backendSetting;
    }

    public void setBackendSetting(String backendSetting) {
        this.backendSetting = backendSetting;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "ProductType{" +
            "id = " + id +
            ", isDelete = " + isDelete +
            ", createdAt = " + createdAt +
            ", createdBy = " + createdBy +
            ", changedAt = " + changedAt +
            ", changedBy = " + changedBy +
            ", type = " + type +
            ", name = " + name +
            ", frontSetting = " + frontSetting +
            ", backendSetting = " + backendSetting +
            ", status = " + status +
            "}";
    }
}
