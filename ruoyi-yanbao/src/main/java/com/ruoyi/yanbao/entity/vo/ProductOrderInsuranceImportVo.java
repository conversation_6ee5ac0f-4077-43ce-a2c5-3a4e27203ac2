package com.ruoyi.yanbao.entity.vo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.annotation.Excel.Type;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 投保管理Excel导入VO
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
public class ProductOrderInsuranceImportVo {
    /**
     * 订单号
     */
    @Excel(name = "订单号", type = Type.ALL, cellType = Excel.ColumnType.NUMERIC, headerBackgroundColor = IndexedColors.RED)
    private Long orderId;

    /**
     * 客户姓名
     */
    @Excel(name = "客户姓名", type = Type.ALL)
    private String customerName;

    /**
     * 车架号
     */
    @Excel(name = "车架号", type = Type.ALL)
    private String vinNo;

    /**
     * 车牌号
     */
    @Excel(name = "车牌号", type = Type.ALL)
    private String carNo;

    /**
     * 产品名称
     */
    @Excel(name = "产品名称", type = Type.ALL)
    private String productName;

    /**
     * 门店名称
     */
    @Excel(name = "门店名称", type = Type.ALL)
    private String storeName;

    /**
     * 销售员姓名
     */
    @Excel(name = "销售员姓名", type = Type.ALL)
    private String saleUserName;

    /**
     * 保险公司名称
     */
    @Excel(name = "保险公司名称", type = Type.ALL)
    private String insuranceCompanyName;

    /**
     * 保单号
     */
    @Excel(name = "保单号", type = Type.ALL, headerBackgroundColor = IndexedColors.RED)
    private String insuranceNo;

    /**
     * 投保时间
     */
    @Excel(name = "投保时间", dateFormat = "yyyy-MM-dd", type = Type.ALL, headerBackgroundColor = IndexedColors.RED)
    private Date insuranceDate;

    /**
     * 保险开始时间
     */
    @Excel(name = "保险开始时间", dateFormat = "yyyy-MM-dd", type = Type.ALL, headerBackgroundColor = IndexedColors.RED)
    private Date beginTime;

    /**
     * 保险结束时间
     */
    @Excel(name = "保险结束时间", dateFormat = "yyyy-MM-dd", type = Type.ALL, headerBackgroundColor = IndexedColors.RED)
    private Date endTime;

    /**
     * 投保金额
     */
    @Excel(name = "投保金额", type = Type.ALL, cellType = Excel.ColumnType.NUMERIC, headerBackgroundColor = IndexedColors.RED)
    private BigDecimal price;

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getVinNo() {
        return vinNo;
    }

    public void setVinNo(String vinNo) {
        this.vinNo = vinNo;
    }

    public String getCarNo() {
        return carNo;
    }

    public void setCarNo(String carNo) {
        this.carNo = carNo;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getSaleUserName() {
        return saleUserName;
    }

    public void setSaleUserName(String saleUserName) {
        this.saleUserName = saleUserName;
    }

    public String getInsuranceCompanyName() {
        return insuranceCompanyName;
    }

    public void setInsuranceCompanyName(String insuranceCompanyName) {
        this.insuranceCompanyName = insuranceCompanyName;
    }

    public String getInsuranceNo() {
        return insuranceNo;
    }

    public void setInsuranceNo(String insuranceNo) {
        this.insuranceNo = insuranceNo;
    }

    public Date getInsuranceDate() {
        return insuranceDate;
    }

    public void setInsuranceDate(Date insuranceDate) {
        this.insuranceDate = insuranceDate;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    @Override
    public String toString() {
        return "ProductOrderInsuranceImportVo{" +
                "orderId='" + orderId + '\'' +
                ", customerName='" + customerName + '\'' +
                ", vinNo='" + vinNo + '\'' +
                ", carNo='" + carNo + '\'' +
                ", productName='" + productName + '\'' +
                ", storeName='" + storeName + '\'' +
                ", saleUserName='" + saleUserName + '\'' +
                ", insuranceCompanyName='" + insuranceCompanyName + '\'' +
                ", insuranceNo='" + insuranceNo + '\'' +
                ", insuranceDate=" + insuranceDate +
                ", beginTime=" + beginTime +
                ", endTime=" + endTime +
                ", price=" + price +
                '}';
    }
}
