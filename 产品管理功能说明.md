# 产品管理功能说明

## 功能概述
产品管理页面提供了对产品的完整管理功能，包括产品基本信息管理和保障期限的一对多关系管理。产品类型只显示生效状态的类型。

## 功能特性
- ✅ 产品列表查询（支持按产品名称、产品类型、销售模式、状态筛选）
- ✅ 产品新增（包含基本信息和保障期限）
- ✅ 产品编辑（包含基本信息和保障期限）
- ✅ 产品状态修改（启用/停用）
- ✅ 产品与保障期限一对多关系管理
- ✅ 产品类型只显示生效的类型
- ✅ 保险公司、门店选择

## 数据关系
- **产品 ↔ 产品类型**：多对一关系，产品类型只显示状态为生效(1)的记录
- **产品 ↔ 保障期限**：一对多关系，一个产品可以有多个保障期限
- **产品 ↔ 保险公司**：多对一关系
- **产品 ↔ 门店**：多对多关系，通过逗号分隔的ID字符串存储

## 部署步骤

### 1. 数据库更新
执行以下SQL文件来添加必要的菜单：

```sql
-- 添加菜单权限
source sql/product_manage_menu.sql
```

### 2. 后端代码
已创建和修改的文件：
- `ruoyi-yanbao/src/main/java/com/ruoyi/yanbao/service/ProductService.java` - 扩展服务接口
- `ruoyi-yanbao/src/main/java/com/ruoyi/yanbao/service/impl/ProductServiceImpl.java` - 实现服务方法
- `ruoyi-yanbao/src/main/java/com/ruoyi/yanbao/service/ProductTypeService.java` - 扩展产品类型服务
- `ruoyi-yanbao/src/main/java/com/ruoyi/yanbao/service/impl/ProductTypeServiceImpl.java` - 实现产品类型服务
- `ruoyi-yanbao/src/main/java/com/ruoyi/yanbao/mapper/ProductTermMapper.java` - 扩展保障期限Mapper
- `ruoyi-yanbao/src/main/resources/mapper/ProductTermMapper.xml` - 添加批量插入方法
- `ruoyi-admin/src/main/java/com/ruoyi/web/controller/yanbao/ProductManageController.java` - 新建控制器

### 3. 前端代码
已创建的文件：
- `ruoyi-ui/src/api/yanbao/productManage.js` - API接口
- `ruoyi-ui/src/views/yanbao/productManage/index.vue` - 管理页面

## 页面功能说明

### 查询功能
- 支持按产品名称模糊查询
- 支持按产品类型筛选（只显示生效的产品类型）
- 支持按销售模式筛选（自营/代销）
- 支持按状态筛选（有效/无效）

### 新增/编辑功能
页面采用标签页设计，分为两个部分：

#### 基本信息标签页
- **产品名称**：必填
- **产品类型**：必填，下拉选择（只显示生效的产品类型）
- **销售模式**：必填，自营/代销
- **保险公司**：必填，下拉选择
- **最大里程数**：数字输入，单位公里
- **最大期限**：数字输入，可选择月/日单位
- **免赔期**：数字输入，单位天
- **允许分期**：单选，允许/不允许
- **状态**：必填，有效/无效
- **排序**：数字输入
- **适用门店**：多选，不选则适用全部门店
- **购买限制**：文本域
- **产品说明**：文本域

#### 保障期限标签页
- 支持动态添加/删除保障期限
- 每个保障期限包含：期限名称、期限(月)、里程(公里)
- 表格形式展示，支持内联编辑

### 状态管理
- 表格中的状态列提供开关按钮，可直接切换启用/停用状态
- 状态变更会有确认提示

## 权限配置
系统会自动创建以下权限：
- `yanbao:productManage:list` - 产品查询
- `yanbao:productManage:query` - 产品详情查询
- `yanbao:productManage:add` - 产品新增
- `yanbao:productManage:edit` - 产品编辑

## 菜单位置
延保管理 > 产品管理

## 技术特点

### 后端特点
1. **事务管理**：产品和保障期限的保存/更新使用事务确保数据一致性
2. **批量操作**：保障期限使用批量插入提高性能
3. **级联操作**：更新产品时会先删除原有保障期限再插入新的
4. **数据验证**：完整的参数验证和权限控制

### 前端特点
1. **标签页设计**：基本信息和保障期限分离，界面清晰
2. **动态表格**：保障期限支持动态添加/删除
3. **联动选择**：产品类型只显示生效的记录
4. **数据转换**：适用门店的多选值与后端字符串格式自动转换

## 注意事项
1. 产品类型必须先在产品类型管理中设置为生效状态才能在产品管理中选择
2. 保障期限至少需要添加一个，否则产品功能不完整
3. 适用门店为空时表示适用于全部门店
4. 删除产品时会级联删除相关的保障期限记录
5. 确保保险公司和门店数据已存在且状态为生效
