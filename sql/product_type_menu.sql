-- 产品类型管理菜单 SQL
-- 注意：此SQL假设延保管理父级菜单已存在，如果不存在请先执行 yanbao_complete_menu.sql

-- 产品类型管理菜单
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('产品类型管理', (SELECT menu_id FROM sys_menu WHERE menu_name = '延保管理' AND parent_id = 0 LIMIT 1), '7', 'productType', 'yanbao/productType/index', 1, 0, 'C', '0', '0', 'yanbao:productType:list', 'dict', 'admin', sysdate(), '', null, '产品类型管理菜单');

-- 获取产品类型管理菜单ID
SELECT @productTypeParentId := LAST_INSERT_ID();

-- 产品类型管理按钮
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('产品类型查询', @productTypeParentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'yanbao:productType:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('产品类型修改', @productTypeParentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'yanbao:productType:edit',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('产品类型导出', @productTypeParentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'yanbao:productType:export',       '#', 'admin', sysdate(), '', null, '');
